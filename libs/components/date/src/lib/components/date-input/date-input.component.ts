import {
  AbstractValueAccessor,
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';

import { NgClass } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormsModule,
  UntypedFormControl,
  ValidationErrors,
} from '@angular/forms';
import { InputAddonComponent } from '@fincloud/components/forms';
import { IconComponent } from '@fincloud/components/icons';
import { DateService } from '@fincloud/core/date';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import {
  NgbCalendar,
  NgbDate,
  NgbDateAdapter,
  NgbDateParserFormatter,
  NgbDateStruct,
  NgbInputDatepicker,
  PlacementArray,
} from '@ng-bootstrap/ng-bootstrap';
import * as dayjs from 'dayjs';
import { isArray } from 'lodash-es';
import { CustomAdapter } from '../../services/custom-adapter.service';
import { CustomDateParserFormatter } from '../../services/custom-data-parse-formater.service';

@Component({
  selector: 'ui-date-input',
  templateUrl: './date-input.component.html',
  styleUrls: ['./date-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    makeControlValueAccessorProvider(DateInputComponent),
    makeControlValidatorProvider(DateInputComponent),
    { provide: NgbDateAdapter, useClass: CustomAdapter },
    { provide: NgbDateParserFormatter, useClass: CustomDateParserFormatter },
  ],
  imports: [
    InputAddonComponent,
    NgClass,
    NgbInputDatepicker,
    FormsModule,
    IconComponent,
  ],
})
export class DateInputComponent
  extends AbstractValueAccessor<string>
  implements OnInit
{
  @Input() bckgColor: 'background' | '' = '';
  @Input() minDate = '1900-01-01';
  @Input() maxDate = '2099-12-31';
  @Input() placements: PlacementArray = [];
  @Input() disable = false;
  @Input() minAllowedDate: NgbDateStruct;
  @Input() maxAllowedDate: NgbDateStruct;
  @Input() customErrorInput = false;
  @Input() isReadOnly = false;

  @Input()
  hasBorder = false;

  @Input()
  size: 'small' | 'large' = 'small';

  @Output() dateValueChanged = new EventEmitter<void>();

  showCalendarOnTop = false;
  dateFormat: string;

  constructor(
    private ngbCalendar: NgbCalendar,
    private dateAdapter: NgbDateAdapter<string>,
    private dateService: DateService,
    changeDetectorRef: ChangeDetectorRef,
    private regionalSettings: RegionalSettingsService,
  ) {
    super(changeDetectorRef);
    this.dateFormat = this.regionalSettings.dateFormat;
  }

  ngOnInit() {
    const allPlacements = isArray(this.placements)
      ? this.placements.join(' ')
      : this.placements;

    this.showCalendarOnTop = allPlacements.indexOf('top') >= 0;
  }

  validate(control: UntypedFormControl): ValidationErrors | null {
    super.validate(control);

    if (control.value == null || control.value === '') {
      return null;
    }

    if (!this.dateService.isValidDate(control.value)) {
      return { invalidDate: true };
    }

    return dayjs(control.value).isBefore(this.minDate) ||
      dayjs(control.value).isAfter(this.maxDate)
      ? { invalidDate: true }
      : null;
  }

  clearDate(): void {
    this.value = null;
    this.saveDate();
  }

  setToday(): void {
    this.value = this.dateAdapter.toModel(this.ngbCalendar.getToday());
    this.saveDate();
  }

  isDisable(): (
    date: NgbDateStruct,
    current: {
      month: number;
      year: number;
    },
  ) => boolean {
    return (date: NgbDateStruct, current: { month: number; year: number }) => {
      const selectedDate = NgbDate.from(date);

      // Convert minDate2 and maxDate2 to NgbDate objects
      const minAllowedDate = NgbDate.from(this.minAllowedDate);
      const maxAllowedDate = NgbDate.from(this.maxAllowedDate);

      // Disable the date if it's before minDate2 or after maxDate2
      if (
        selectedDate.before(minAllowedDate) ||
        selectedDate.after(maxAllowedDate)
      ) {
        return true;
      }

      // If none of the above conditions are met, the date is enabled
      return false;
    };
  }

  get hasError() {
    return (
      (this.formControl?.errors && !this.formControl?.pristine) ||
      this.customErrorInput
    );
  }

  saveDate(): void {
    this.dateValueChanged.emit();
  }
}
