import { Component, Input } from '@angular/core';
import { ModalService } from '@fincloud/core/modal';
import { StateLibBusinessCasePageActions } from '@fincloud/state/business-case';
import { CustomerManagementControllerService } from '@fincloud/swagger-generator/authorization-server';
import {
  BusinessCaseControllerService,
  Group,
  InterestedCustomers,
  InterestedCustomersControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import { CadrGroup, Company } from '@fincloud/swagger-generator/company';
import { FinIconComponent } from '@fincloud/ui/icon';
import { FinModalService } from '@fincloud/ui/modal';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { partition, uniq } from 'lodash-es';
import { EMPTY, catchError, filter, map, of, switchMap, tap } from 'rxjs';
import { DataRoomVisibilityCustomer } from '../../models/data-room-visibility-customer';
import { CompanyDataRoomHelperService } from '../../services/company-data-room-helper.service';
import { DataRoomAccessModalComponent } from '../data-room-access-modal/data-room-access-modal.component';

@Component({
  selector: 'app-group-visibility-card',
  templateUrl: './group-visibility-card.component.html',
  styleUrls: ['./group-visibility-card.component.scss'],
  imports: [FinIconComponent],
})
export class GroupVisibilityCardComponent {
  @Input()
  groups: (Group | CadrGroup)[];

  @Input()
  participants: DataRoomVisibilityCustomer[];

  @Input()
  company: Company;

  @Input()
  businessCaseId: string;

  allApplicantsAndInvitees: DataRoomVisibilityCustomer[];
  explicitlySharedCustomers: DataRoomVisibilityCustomer[];
  implicitlySharedCustomers: DataRoomVisibilityCustomer[];

  constructor(
    private modalService: ModalService,
    private interestedCustomersControllerService: InterestedCustomersControllerService,
    private cadrHelperService: CompanyDataRoomHelperService,
    private businessCaseService: BusinessCaseControllerService,
    private store: Store,
    private customerManagementControllerService: CustomerManagementControllerService,
    private finModalService: FinModalService,
  ) {}

  openActionsModal() {
    this.setCustomersAndExtraCustomers()
      .pipe(
        switchMap(() =>
          this.finModalService
            .open<any | any, any, DataRoomAccessModalComponent>(
              DataRoomAccessModalComponent,
              {
                data: {
                  groups: this.groups,
                  customers: !this.company
                    ? this.participants
                    : this.implicitlySharedCustomers,
                  extraCustomersAccess: !this.company
                    ? this.allApplicantsAndInvitees
                    : this.explicitlySharedCustomers,
                  isCADR: !!this.company,
                },
                disableClose: true,
                size: FinSize.M,
              },
            )
            .afterClosed(),
        ),
        filter((result) => !!result),
        switchMap((groupsModified: (Group | CadrGroup)[]) => {
          if (this.company) {
            this.cadrHelperService.manageGroupsChange(
              this.company.id,
              groupsModified,
            );
            return of(null);
          } else {
            return this.businessCaseService
              .addFieldAndEditBusinessCaseGroups({
                businessCaseId: this.businessCaseId,
                body: {
                  groupsOrdered: groupsModified,
                },
              })
              .pipe(
                tap((businessCase) => {
                  this.store.dispatch(
                    StateLibBusinessCasePageActions.setBusinessCaseGroups({
                      payload:
                        businessCase.businessCaseTemplate.template
                          .groupsOrdered,
                    }),
                  );
                }),
                catchError(() => EMPTY),
              );
          }
        }),
      )
      .subscribe();
  }

  private setCustomersAndExtraCustomers() {
    if (!this.company) {
      // TODO: Ideally have some event from BE and move to store
      return this.interestedCustomersControllerService
        .getInterestedCustomersForBusinessCase({
          businessCaseId: this.businessCaseId,
        })
        .pipe(
          switchMap((interestedCustomers) => {
            const allCustomersKeys =
              this.getInterestedCustomersKeysArray(interestedCustomers);

            return this.customerManagementControllerService
              .getMultipleCustomers({ customerKeys: uniq(allCustomersKeys) })
              .pipe(
                map((customersInfo) =>
                  customersInfo.map((c) => {
                    return {
                      customerKey: c.key,
                      customerName: c.name,
                    };
                  }),
                ),
              );
          }),
          tap(
            (applicantsAndInvitees) =>
              (this.allApplicantsAndInvitees = applicantsAndInvitees),
          ),
          catchError(() => {
            this.allApplicantsAndInvitees;
            return of([]);
          }),
        );
    } else {
      [this.explicitlySharedCustomers, this.implicitlySharedCustomers] =
        partition(this.participants, (p) => p.shareType === 'EXPLICIT');

      return of(null);
    }
  }

  private getInterestedCustomersKeysArray(
    interestedCustomers: InterestedCustomers,
  ) {
    return [
      ...interestedCustomers.applicants,
      ...interestedCustomers.communicators,
      ...interestedCustomers.invitees,
    ];
  }
}
